import * as FileSystem from 'expo-file-system';
import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import * as DocumentPicker from 'expo-document-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert, Platform } from 'react-native';
import { shareAsync } from 'expo-sharing';

// Constants
export const BACKUP_TASK_NAME = 'database-backup-task';
export const BACKUP_DIR = `${FileSystem.documentDirectory}backups/`;
export const DATABASE_PATH = `${FileSystem.documentDirectory}SQLite/crm.db`;
export const MAX_BACKUPS = 5;

// Scheduled backup times (5 times per day): 6 AM, 10 AM, 2 PM, 6 PM, 10 PM
export const BACKUP_SCHEDULE_HOURS = [6, 10, 14, 18, 22];
export const BACKUP_INTERVAL_HOURS = 4.8; // Fallback interval (24/5 hours)

// Storage keys
const LAST_BACKUP_KEY = 'last_backup_timestamp';
const AUTO_BACKUP_ENABLED_KEY = 'auto_backup_enabled';
const BACKUP_COUNT_KEY = 'backup_count';
const NEXT_SCHEDULED_BACKUP_KEY = 'next_scheduled_backup';

// Interfaces
export interface BackupInfo {
  filename: string;
  path: string;
  timestamp: number;
  size: number;
  formattedDate: string;
}

export interface BackupStatus {
  isEnabled: boolean;
  lastBackup: number | null;
  nextBackup: number | null;
  backupCount: number;
}

export interface BackupValidationResult {
  isValid: boolean;
  error?: string;
  fileSize?: number;
  isDatabase?: boolean;
}

export interface BackupProgress {
  stage: 'validating' | 'backing_up' | 'restoring' | 'cleaning_up' | 'complete';
  progress: number; // 0-100
  message: string;
}

// Ensure backup directory exists
export const ensureBackupDirectoryExists = async (): Promise<void> => {
  try {
    const dirInfo = await FileSystem.getInfoAsync(BACKUP_DIR);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(BACKUP_DIR, { intermediates: true });
      console.log('Created backup directory:', BACKUP_DIR);
    }
  } catch (error) {
    console.error('Error creating backup directory:', error);
    throw error;
  }
};

// Get database file location for display to user
export const getDatabaseLocation = (): string => {
  return DATABASE_PATH;
};

// Get backup directory location for display to user
export const getBackupLocation = (): string => {
  return BACKUP_DIR;
};

// Format backup date consistently
export const formatBackupDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  });
};

// Generate backup filename with timestamp
const generateBackupFilename = (): string => {
  const timestamp = Date.now();
  const date = new Date(timestamp);
  const dateStr = date.toISOString().replace(/[:.]/g, '-');
  return `crm_backup_${timestamp}_${dateStr}.db`;
};

// Create a manual backup
export const createBackup = async (): Promise<BackupInfo> => {
  try {
    await ensureBackupDirectoryExists();

    // Check if database file exists
    const dbInfo = await FileSystem.getInfoAsync(DATABASE_PATH);
    if (!dbInfo.exists) {
      throw new Error('Database file not found');
    }

    const filename = generateBackupFilename();
    const backupPath = `${BACKUP_DIR}${filename}`;

    console.log(`Creating backup with filename: ${filename}`);

    // Copy database file to backup location
    await FileSystem.copyAsync({
      from: DATABASE_PATH,
      to: backupPath,
    });

    // Get file info
    const backupInfo = await FileSystem.getInfoAsync(backupPath);
    const timestamp = Date.now();

    // Update storage
    await AsyncStorage.setItem(LAST_BACKUP_KEY, timestamp.toString());

    // Update backup count
    const currentCount = await getBackupCount();
    await AsyncStorage.setItem(BACKUP_COUNT_KEY, (currentCount + 1).toString());

    const backup: BackupInfo = {
      filename,
      path: backupPath,
      timestamp,
      size: (backupInfo as any).size || 0,
      formattedDate: formatBackupDate(timestamp),
    };

    console.log('Backup created successfully:', backup);

    // Clean up old backups
    await cleanupOldBackups();

    return backup;
  } catch (error) {
    console.error('Error creating backup:', error);
    throw error;
  }
};

// Get list of all backups
export const getBackupList = async (): Promise<BackupInfo[]> => {
  try {
    await ensureBackupDirectoryExists();

    const backupDir = await FileSystem.readDirectoryAsync(BACKUP_DIR);
    const backups: BackupInfo[] = [];

    for (const filename of backupDir) {
      if (filename.endsWith('.db')) {
        const path = `${BACKUP_DIR}${filename}`;
        const fileInfo = await FileSystem.getInfoAsync(path);

        // Extract timestamp from filename
        // New format: crm_backup_1234567890_2023-12-25T10-30-00-000Z.db
        // Old format: crm_backup_2023-12-25T10-30-00-000Z.db
        let timestamp = (fileInfo as any).modificationTime || Date.now();

        const newFormatMatch = filename.match(/crm_backup_(\d+)_(.+)\.db/);
        const oldFormatMatch = filename.match(/crm_backup_(.+)\.db/);

        if (newFormatMatch) {
          // Use the numeric timestamp from the new format
          timestamp = parseInt(newFormatMatch[1], 10);
          console.log(`Parsed new format timestamp: ${timestamp} for file: ${filename}`);
        } else if (oldFormatMatch) {
          try {
            // Try to parse the old ISO string format
            const isoString = oldFormatMatch[1].replace(/-/g, ':');
            const parsedDate = new Date(isoString);
            if (!isNaN(parsedDate.getTime())) {
              timestamp = parsedDate.getTime();
              console.log(`Parsed old format timestamp: ${timestamp} for file: ${filename}`);
            }
          } catch (e) {
            // Fallback to file modification time
            timestamp = (fileInfo as any).modificationTime || Date.now();
            console.log(`Using fallback timestamp: ${timestamp} for file: ${filename}`);
          }
        }

        const formattedDate = formatBackupDate(timestamp);
        console.log(`Backup file: ${filename}, timestamp: ${timestamp}, formatted: ${formattedDate}`);

        backups.push({
          filename,
          path,
          timestamp,
          size: (fileInfo as any).size || 0,
          formattedDate,
        });
      }
    }

    // Sort by timestamp (newest first)
    return backups.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error getting backup list:', error);
    return [];
  }
};

// Clean up old backups (keep only MAX_BACKUPS)
export const cleanupOldBackups = async (): Promise<void> => {
  try {
    const backups = await getBackupList();

    if (backups.length > MAX_BACKUPS) {
      const backupsToDelete = backups.slice(MAX_BACKUPS);

      for (const backup of backupsToDelete) {
        await FileSystem.deleteAsync(backup.path, { idempotent: true });
        console.log('Deleted old backup:', backup.filename);
      }

      // Update backup count
      await AsyncStorage.setItem(BACKUP_COUNT_KEY, MAX_BACKUPS.toString());
    }
  } catch (error) {
    console.error('Error cleaning up old backups:', error);
  }
};

// Restore database from backup
export const restoreFromBackup = async (backupPath: string): Promise<void> => {
  try {
    // Check if backup file exists
    const backupInfo = await FileSystem.getInfoAsync(backupPath);
    if (!backupInfo.exists) {
      throw new Error('Backup file not found');
    }

    // Create a backup of current database before restoring
    await createBackup();

    // Copy backup file to database location
    await FileSystem.copyAsync({
      from: backupPath,
      to: DATABASE_PATH,
    });

    console.log('Database restored successfully from:', backupPath);
  } catch (error) {
    console.error('Error restoring from backup:', error);
    throw error;
  }
};

// Get backup count
export const getBackupCount = async (): Promise<number> => {
  try {
    const count = await AsyncStorage.getItem(BACKUP_COUNT_KEY);
    return count ? parseInt(count, 10) : 0;
  } catch (error) {
    console.error('Error getting backup count:', error);
    return 0;
  }
};

// Get backup status
export const getBackupStatus = async (): Promise<BackupStatus> => {
  try {
    const isEnabled = await isAutoBackupEnabled();
    const lastBackupStr = await AsyncStorage.getItem(LAST_BACKUP_KEY);
    const lastBackup = lastBackupStr ? parseInt(lastBackupStr, 10) : null;
    const backupCount = await getBackupCount();

    let nextBackup: number | null = null;
    if (isEnabled) {
      nextBackup = getNextScheduledBackupTime();
    }

    return {
      isEnabled,
      lastBackup,
      nextBackup,
      backupCount,
    };
  } catch (error) {
    console.error('Error getting backup status:', error);
    return {
      isEnabled: false,
      lastBackup: null,
      nextBackup: null,
      backupCount: 0,
    };
  }
};

// Check if auto backup is enabled
export const isAutoBackupEnabled = async (): Promise<boolean> => {
  try {
    const enabled = await AsyncStorage.getItem(AUTO_BACKUP_ENABLED_KEY);
    return enabled === 'true';
  } catch (error) {
    console.error('Error checking auto backup status:', error);
    return false;
  }
};

// Enable/disable auto backup
export const setAutoBackupEnabled = async (enabled: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(AUTO_BACKUP_ENABLED_KEY, enabled.toString());

    if (enabled) {
      await startAutoBackup();
    } else {
      await stopAutoBackup();
    }
  } catch (error) {
    console.error('Error setting auto backup status:', error);
    throw error;
  }
};

// Get next scheduled backup time
export const getNextScheduledBackupTime = (): number => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();

  // Find the next scheduled hour
  let nextHour = BACKUP_SCHEDULE_HOURS.find(hour => hour > currentHour);

  // If no hour found today, use the first hour of tomorrow
  if (!nextHour) {
    nextHour = BACKUP_SCHEDULE_HOURS[0];
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(nextHour, 0, 0, 0);
    return tomorrow.getTime();
  }

  // Set to next scheduled hour today
  const nextBackup = new Date(now);
  nextBackup.setHours(nextHour, 0, 0, 0);

  // If we're past the minute mark for this hour, move to next scheduled time
  if (nextHour === currentHour && currentMinutes > 5) {
    return getNextScheduledBackupTime();
  }

  return nextBackup.getTime();
};

// Check if it's time for a scheduled backup
export const isScheduledBackupTime = (): boolean => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();

  // Check if current time matches any scheduled backup time (within 5 minutes)
  return BACKUP_SCHEDULE_HOURS.includes(currentHour) && currentMinutes <= 5;
};

// Background task definition
TaskManager.defineTask(BACKUP_TASK_NAME, async () => {
  try {
    console.log('Background backup task started');

    // Check if auto backup is enabled
    const isEnabled = await isAutoBackupEnabled();
    if (!isEnabled) {
      console.log('Auto backup is disabled, skipping');
      return BackgroundFetch.BackgroundFetchResult.NoData;
    }

    // Check if it's a scheduled backup time
    if (!isScheduledBackupTime()) {
      // Fallback: Check if enough time has passed since last backup
      const lastBackupStr = await AsyncStorage.getItem(LAST_BACKUP_KEY);
      const lastBackup = lastBackupStr ? parseInt(lastBackupStr, 10) : 0;
      const now = Date.now();
      const timeSinceLastBackup = now - lastBackup;
      const backupInterval = BACKUP_INTERVAL_HOURS * 60 * 60 * 1000; // Convert to milliseconds

      if (timeSinceLastBackup < backupInterval) {
        console.log('Not time for backup yet');
        return BackgroundFetch.BackgroundFetchResult.NoData;
      }
    }

    // Create backup
    await createBackup();
    console.log('Background backup completed successfully');

    return BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    console.error('Background backup failed:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

// Start auto backup
export const startAutoBackup = async (): Promise<void> => {
  try {
    // Check if background fetch is available
    const status = await BackgroundFetch.getStatusAsync();
    if (status === BackgroundFetch.BackgroundFetchStatus.Restricted ||
        status === BackgroundFetch.BackgroundFetchStatus.Denied) {
      throw new Error('Background fetch is not available');
    }

    // Register background task
    await BackgroundFetch.registerTaskAsync(BACKUP_TASK_NAME, {
      minimumInterval: BACKUP_INTERVAL_HOURS * 60 * 60, // Convert to seconds
      stopOnTerminate: false,
      startOnBoot: true,
    });

    console.log('Auto backup started');
  } catch (error) {
    console.error('Error starting auto backup:', error);
    throw error;
  }
};

// Stop auto backup
export const stopAutoBackup = async (): Promise<void> => {
  try {
    await BackgroundFetch.unregisterTaskAsync(BACKUP_TASK_NAME);
    console.log('Auto backup stopped');
  } catch (error) {
    console.error('Error stopping auto backup:', error);
    throw error;
  }
};

// Initialize backup system
export const initializeBackupSystem = async (): Promise<void> => {
  try {
    await ensureBackupDirectoryExists();

    // Check if auto backup should be enabled
    const isEnabled = await isAutoBackupEnabled();
    if (isEnabled) {
      await startAutoBackup();
    }

    console.log('Backup system initialized');
  } catch (error) {
    console.error('Error initializing backup system:', error);
  }
};

// Delete a specific backup
export const deleteBackup = async (backupPath: string): Promise<void> => {
  try {
    await FileSystem.deleteAsync(backupPath, { idempotent: true });

    // Update backup count
    const currentCount = await getBackupCount();
    if (currentCount > 0) {
      await AsyncStorage.setItem(BACKUP_COUNT_KEY, (currentCount - 1).toString());
    }

    console.log('Backup deleted:', backupPath);
  } catch (error) {
    console.error('Error deleting backup:', error);
    throw error;
  }
};

// Format file size for display
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Get the latest backup file
export const getLatestBackup = async (): Promise<BackupInfo | null> => {
  try {
    const backups = await getBackupList();
    return backups.length > 0 ? backups[0] : null; // First item is the latest due to sorting
  } catch (error) {
    console.error('Error getting latest backup:', error);
    return null;
  }
};

// Validate backup file integrity
export const validateBackupFile = async (filePath: string): Promise<BackupValidationResult> => {
  try {
    // Check if file exists
    const fileInfo = await FileSystem.getInfoAsync(filePath);
    if (!fileInfo.exists) {
      return {
        isValid: false,
        error: 'Backup file not found'
      };
    }

    // Check file size (should be > 0 and reasonable for a database)
    const fileSize = (fileInfo as any).size || 0;
    if (fileSize === 0) {
      return {
        isValid: false,
        error: 'Backup file is empty'
      };
    }

    if (fileSize < 1024) { // Less than 1KB is suspicious for a database
      return {
        isValid: false,
        error: 'Backup file is too small to be a valid database'
      };
    }

    // Check file extension
    const isDatabase = filePath.toLowerCase().endsWith('.db');
    if (!isDatabase) {
      return {
        isValid: false,
        error: 'File must have a .db extension'
      };
    }

    // Basic SQLite file header check
    try {
      const headerBytes = await FileSystem.readAsStringAsync(filePath, {
        encoding: FileSystem.EncodingType.Base64,
        length: 16
      });

      // Convert base64 to string and check for SQLite header
      const headerString = atob(headerBytes);
      if (!headerString.startsWith('SQLite format 3')) {
        return {
          isValid: false,
          error: 'File does not appear to be a valid SQLite database'
        };
      }
    } catch (headerError) {
      console.warn('Could not validate SQLite header:', headerError);
      // Continue with validation - header check is not critical
    }

    return {
      isValid: true,
      fileSize,
      isDatabase: true
    };
  } catch (error) {
    console.error('Error validating backup file:', error);
    return {
      isValid: false,
      error: `Validation failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
};

// Download the latest backup - allows user to choose save location
export const downloadLatestBackup = async (): Promise<void> => {
  try {
    const latestBackup = await getLatestBackup();

    if (!latestBackup) {
      throw new Error('No backup files found');
    }

    // Check if backup file exists
    const backupInfo = await FileSystem.getInfoAsync(latestBackup.path);
    if (!backupInfo.exists) {
      throw new Error('Latest backup file not found');
    }

    // Create a filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const fileName = `SAMVIDA_backup_${timestamp}.db`;

    // Create a temporary file with the proper name for sharing
    const tempPath = `${FileSystem.cacheDirectory}${fileName}`;

    // Copy the backup to temp location with proper filename
    await FileSystem.copyAsync({
      from: latestBackup.path,
      to: tempPath,
    });

    // Use sharing to let user choose where to save
    await shareAsync(tempPath, {
      UTI: '.db',
      mimeType: 'application/x-sqlite3',
      dialogTitle: 'Save Backup File'
    });

    // Clean up the temporary file after a delay to ensure sharing is complete
    setTimeout(async () => {
      try {
        await FileSystem.deleteAsync(tempPath, { idempotent: true });
      } catch (cleanupError) {
        console.log('Error cleaning up temp file:', cleanupError);
      }
    }, 5000); // 5 second delay

    console.log(`Backup shared successfully: ${fileName}`);
  } catch (error) {
    console.error('Error downloading latest backup:', error);
    throw error;
  }
};

// Export backup with file picker - allows user to choose destination
export const exportBackupWithPicker = async (backupPath?: string): Promise<void> => {
  try {
    let sourceBackup: BackupInfo | null = null;

    if (backupPath) {
      // Use specific backup
      const backups = await getBackupList();
      sourceBackup = backups.find(b => b.path === backupPath) || null;
    } else {
      // Use latest backup
      sourceBackup = await getLatestBackup();
    }

    if (!sourceBackup) {
      throw new Error('No backup file found to export');
    }

    // Validate the backup file
    const validation = await validateBackupFile(sourceBackup.path);
    if (!validation.isValid) {
      throw new Error(`Invalid backup file: ${validation.error}`);
    }

    // Create a filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const fileName = `SAMVIDA_backup_${timestamp}.db`;

    // Create a temporary file with the proper name for sharing
    const tempPath = `${FileSystem.cacheDirectory}${fileName}`;

    // Copy the backup to temp location with proper filename
    await FileSystem.copyAsync({
      from: sourceBackup.path,
      to: tempPath,
    });

    // Use sharing to let user choose where to save
    await shareAsync(tempPath, {
      UTI: '.db',
      mimeType: 'application/x-sqlite3',
      dialogTitle: 'Export Backup File'
    });

    // Clean up the temporary file after a delay
    setTimeout(async () => {
      try {
        await FileSystem.deleteAsync(tempPath, { idempotent: true });
      } catch (cleanupError) {
        console.log('Error cleaning up temp file:', cleanupError);
      }
    }, 5000);

    console.log(`Backup exported successfully: ${fileName}`);
  } catch (error) {
    console.error('Error exporting backup:', error);
    throw error;
  }
};

// Import backup from external file
export const importBackupFromFile = async (): Promise<string> => {
  try {
    // Use document picker to select backup file
    const result = await DocumentPicker.getDocumentAsync({
      type: ['application/x-sqlite3', 'application/octet-stream', '*/*'],
      copyToCacheDirectory: true,
    });

    if (result.canceled) {
      throw new Error('File selection cancelled');
    }

    const file = result.assets[0];

    // Validate the selected file
    const validation = await validateBackupFile(file.uri);
    if (!validation.isValid) {
      throw new Error(`Invalid backup file: ${validation.error}`);
    }

    // Create a unique filename for the imported backup
    const timestamp = Date.now();
    const filename = `imported_backup_${timestamp}_${file.name || 'backup.db'}`;
    const importPath = `${BACKUP_DIR}${filename}`;

    // Ensure backup directory exists
    await ensureBackupDirectoryExists();

    // Copy the imported file to backup directory
    await FileSystem.copyAsync({
      from: file.uri,
      to: importPath,
    });

    console.log(`Backup imported successfully: ${filename}`);
    return importPath;
  } catch (error) {
    console.error('Error importing backup:', error);
    throw error;
  }
};
