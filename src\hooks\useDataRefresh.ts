import { useEffect, useCallback } from 'react';
import { eventBus, EVENTS } from '../utils/eventBus';

/**
 * Custom hook to handle data refresh when database is restored
 * @param refreshCallback - Function to call when data needs to be refreshed
 * @param dependencies - Optional dependencies array for the refresh callback
 */
export const useDataRefresh = (
  refreshCallback: () => void | Promise<void>,
  dependencies: any[] = []
) => {
  const memoizedRefreshCallback = useCallback(refreshCallback, dependencies);

  useEffect(() => {
    // Listen for database restoration events
    const unsubscribe = eventBus.on(EVENTS.DATABASE_RESTORED, async () => {
      console.log('Database restored, refreshing data...');
      try {
        await memoizedRefreshCallback();
      } catch (error) {
        console.error('Error refreshing data after database restore:', error);
      }
    });

    return unsubscribe;
  }, [memoizedRefreshCallback]);
};

/**
 * Hook specifically for screens that need to reload data after database restore
 * @param loadDataFunction - Function that loads/reloads the screen's data
 */
export const useAutoRefreshOnRestore = (loadDataFunction: () => void | Promise<void>) => {
  useDataRefresh(loadDataFunction, []);
};
